import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/mood.dart';
import '../services/mood_service.dart';
import '../widgets/mood_chart.dart';

class MoodTrendScreen extends StatefulWidget {
  const MoodTrendScreen({super.key});

  @override
  State<MoodTrendScreen> createState() => _MoodTrendScreenState();
}

class _MoodTrendScreenState extends State<MoodTrendScreen> {
  final MoodService _moodService = MoodService();
  List<Mood> _recentMoods = [];
  bool _isLoading = true;
  Map<String, dynamic> _moodSummary = {};

  @override
  void initState() {
    super.initState();
    _loadMoodData();
  }

  Future<void> _loadMoodData() async {
    try {
      final moods = await _moodService.getRecentMoods();
      final summary = _calculateMoodSummary(moods);
      
      setState(() {
        _recentMoods = moods;
        _moodSummary = summary;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading mood data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Map<String, dynamic> _calculateMoodSummary(List<Mood> moods) {
    if (moods.isEmpty) {
      return {
        'averageIntensity': 0.0,
        'totalEntries': 0,
        'mostCommonMood': 'None',
        'trend': 'No data',
      };
    }

    // Calculate average intensity
    final totalIntensity = moods.fold<int>(0, (sum, mood) => sum + mood.intensity);
    final averageIntensity = totalIntensity / moods.length;

    // Find most common mood type
    final moodCounts = <String, int>{};
    for (final mood in moods) {
      moodCounts[mood.type] = (moodCounts[mood.type] ?? 0) + 1;
    }
    final mostCommonMood = moodCounts.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    // Calculate trend (comparing first half with second half)
    String trend = 'Stable';
    if (moods.length >= 4) {
      final midPoint = moods.length ~/ 2;
      final firstHalf = moods.sublist(0, midPoint);
      final secondHalf = moods.sublist(midPoint);
      
      final firstAvg = firstHalf.fold<int>(0, (sum, mood) => sum + mood.intensity) / firstHalf.length;
      final secondAvg = secondHalf.fold<int>(0, (sum, mood) => sum + mood.intensity) / secondHalf.length;
      
      if (secondAvg > firstAvg + 1) {
        trend = 'Improving';
      } else if (secondAvg < firstAvg - 1) {
        trend = 'Declining';
      }
    }

    return {
      'averageIntensity': averageIntensity,
      'totalEntries': moods.length,
      'mostCommonMood': mostCommonMood,
      'trend': trend,
    };
  }

  Widget _buildSummaryCard() {
    final l10n = AppLocalizations.of(context)!;
    
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${l10n.last7Days} Summary',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Total Entries',
                    _moodSummary['totalEntries'].toString(),
                    Icons.calendar_today,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Average Intensity',
                    _moodSummary['averageIntensity'].toStringAsFixed(1),
                    Icons.trending_up,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Most Common',
                    _getMoodDisplayName(_moodSummary['mostCommonMood']),
                    Icons.mood,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Trend',
                    _moodSummary['trend'],
                    Icons.show_chart,
                    _getTrendColor(_moodSummary['trend']),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getMoodDisplayName(String moodType) {
    final l10n = AppLocalizations.of(context)!;
    switch (moodType) {
      case 'anxious':
        return l10n.anxious;
      case 'calm':
        return l10n.calm;
      case 'low':
        return l10n.low;
      default:
        return moodType;
    }
  }

  Color _getTrendColor(String trend) {
    switch (trend) {
      case 'Improving':
        return Colors.green;
      case 'Declining':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.moodTrend),
        backgroundColor: Colors.blue[50],
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              _loadMoodData();
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Column(
                children: [
                  _buildSummaryCard(),
                  Card(
                    margin: const EdgeInsets.all(16),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Mood Intensity Over Time',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 16),
                          MoodChart(moods: _recentMoods),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
