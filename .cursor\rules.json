{"rules": [{"id": "moodmend-naming", "description": "Ensure consistent naming for MoodMend components", "pattern": ".*(Screen|Widget|Service)$", "message": "Class names should end with 'Screen', 'Widget', or 'Service' for clarity."}, {"id": "moodmend-l10n", "description": "Ensure all UI strings use AppLocalizations", "pattern": "Text\\(['\"](.*?)['\"]\\)", "message": "Use AppLocalizations for text strings to support internationalization."}, {"id": "moodmend-asset-path", "description": "Ensure assets are referenced from assets/ folder", "pattern": "assets/[^\\s]*", "message": "Asset paths must start with 'assets/' and be declared in pubspec.yaml."}]}