import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../services/notification_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final NotificationService _notificationService = NotificationService();
  bool _reminderEnabled = false;
  TimeOfDay _reminderTime = const TimeOfDay(hour: 9, minute: 0);
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      await _notificationService.initialize();
      final isEnabled = await _notificationService.isReminderEnabled();
      final timeMap = await _notificationService.getReminderTime();
      
      setState(() {
        _reminderEnabled = isEnabled;
        _reminderTime = TimeOfDay(
          hour: timeMap['hour']!,
          minute: timeMap['minute']!,
        );
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _toggleReminder(bool enabled) async {
    try {
      if (enabled) {
        await _notificationService.scheduleDailyReminder(
          _reminderTime.hour,
          _reminderTime.minute,
        );
      } else {
        await _notificationService.cancelDailyReminder();
      }
      
      setState(() {
        _reminderEnabled = enabled;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              enabled 
                  ? 'Daily reminder enabled'
                  : 'Daily reminder disabled',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating reminder: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _selectReminderTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _reminderTime,
    );
    
    if (picked != null && picked != _reminderTime) {
      setState(() {
        _reminderTime = picked;
      });
      
      // If reminder is enabled, update the scheduled time
      if (_reminderEnabled) {
        await _notificationService.scheduleDailyReminder(
          _reminderTime.hour,
          _reminderTime.minute,
        );
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Reminder time updated'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    }
  }

  Widget _buildSettingsTile({
    required String title,
    String? subtitle,
    required IconData icon,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.blue),
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle) : null,
      trailing: trailing,
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.settings),
        backgroundColor: Colors.blue[50],
        elevation: 0,
      ),
      body: ListView(
        children: [
          const SizedBox(height: 16),
          
          // Notifications Section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              'Notifications',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ),
          
          _buildSettingsTile(
            title: l10n.dailyReminder,
            subtitle: _reminderEnabled 
                ? 'Enabled at ${_reminderTime.format(context)}'
                : 'Disabled',
            icon: Icons.notifications,
            trailing: Switch(
              value: _reminderEnabled,
              onChanged: _toggleReminder,
              activeColor: Colors.blue,
            ),
          ),
          
          if (_reminderEnabled)
            _buildSettingsTile(
              title: l10n.reminderTime,
              subtitle: _reminderTime.format(context),
              icon: Icons.access_time,
              trailing: const Icon(Icons.chevron_right),
              onTap: _selectReminderTime,
            ),
          
          const Divider(),
          
          // App Information Section
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              'About',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ),
          
          _buildSettingsTile(
            title: 'App Version',
            subtitle: '1.0.0',
            icon: Icons.info,
          ),
          
          _buildSettingsTile(
            title: 'Privacy Policy',
            subtitle: 'Learn how we protect your data',
            icon: Icons.privacy_tip,
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // In a real app, this would open a privacy policy page
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Privacy policy would be shown here'),
                ),
              );
            },
          ),
          
          _buildSettingsTile(
            title: 'Support',
            subtitle: 'Get help and send feedback',
            icon: Icons.help,
            trailing: const Icon(Icons.chevron_right),
            onTap: () {
              // In a real app, this would open a support page
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Support page would be shown here'),
                ),
              );
            },
          ),
          
          const SizedBox(height: 32),
          
          // App Description
          Padding(
            padding: const EdgeInsets.all(16),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Icon(
                      Icons.favorite,
                      size: 48,
                      color: Colors.red.withOpacity(0.7),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      l10n.appTitle,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'A simple mood healing app to help you track your emotions and find peace.',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
