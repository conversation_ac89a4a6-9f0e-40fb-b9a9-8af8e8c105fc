import 'package:flutter/material.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'config/app_config.dart';
import 'config/app_localization.dart';
import 'screens/mood_record_screen.dart';

void main() {
  tz.initializeTimeZones();
  runApp(const MoodMendApp());
}

class MoodMendApp extends StatelessWidget {
  const MoodMendApp({super.key});

  @override
  Widget build(BuildContext context) {
    final appConfig = AppConfig();

    return MaterialApp(
      title: appConfig.appName,
      theme: appConfig.lightTheme,
      supportedLocales: appConfig.supportedLocales,
      localizationsDelegates: appConfig.localizationsDelegates,
      localeResolutionCallback: AppLocalization.localeResolutionCallback,
      home: const MoodRecordScreen(),
      debugShowCheckedModeBanner: !appConfig.isDebugMode,
    );
  }
}
