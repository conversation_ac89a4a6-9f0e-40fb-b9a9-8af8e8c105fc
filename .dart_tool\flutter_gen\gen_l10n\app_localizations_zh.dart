// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => '情绪疗愈';

  @override
  String get recordMood => '记录你的情绪';

  @override
  String get anxious => '焦虑';

  @override
  String get calm => '平静';

  @override
  String get low => '低落';

  @override
  String get breathingTool => '深呼吸';

  @override
  String get mindfulnessTool => '正念片刻';

  @override
  String get affirmationTool => '积极语句';

  @override
  String get moodIntensity => '情绪强度';

  @override
  String get moodReason => '你在想什么？（可选）';

  @override
  String get save => '保存';

  @override
  String get therapyTools => '疗愈工具';

  @override
  String get moodTrend => '情绪趋势';

  @override
  String get settings => '设置';

  @override
  String get last7Days => '最近7天';

  @override
  String get dailyReminder => '每日提醒';

  @override
  String get reminderTime => '提醒时间';

  @override
  String get language => '语言';

  @override
  String get inhale => '吸气';

  @override
  String get exhale => '呼气';

  @override
  String get start => '开始';

  @override
  String get stop => '停止';

  @override
  String get play => '播放';

  @override
  String get pause => '暂停';

  @override
  String get favorite => '收藏';

  @override
  String get unfavorite => '取消收藏';

  @override
  String get moodSaved => '情绪记录保存成功！';

  @override
  String get encouragementLow => '有这样的感受是正常的。明天又是新的一天。';

  @override
  String get encouragementMedium => '你做得很好！继续照顾好自己。';

  @override
  String get encouragementHigh => '太棒了！你的正能量在闪闪发光。';
}
