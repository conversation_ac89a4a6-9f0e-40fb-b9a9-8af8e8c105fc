# MoodMend Flutter实现文档

## 1. 项目概述
**MoodMend** 是一个简洁的情绪疗愈移动应用，帮助用户记录情绪、使用快速疗愈工具并查看情绪趋势。使用Flutter实现，支持Android和iOS双端，包含国际化支持，包名设计符合规范。

## 2. 项目配置

### 2.1 包名
- **Android**: `com.joyai.moodmend`
- **iOS**: `com.joyai.moodmend`
- **说明**: 使用 `com.joyai` 作为组织标识，`moodmend` 作为应用标识，符合命名规范。

### 2.2 技术栈
- **框架**: Flutter 3.x
- **语言**: Dart
- **依赖**:
  - `flutter_localizations`: 国际化支持
  - `shared_preferences`: 本地存储情绪数据
  - `fl_chart`: 情绪趋势折线图
  - `just_audio`: 播放正念音频
  - `flutter_local_notifications`: 本地提醒
  - `intl`: 日期格式化和国际化
- **支持平台**: Android (API 21+), iOS (12.0+)

### 2.3 国际化支持
- 使用 `flutter_localizations` 和 `intl` 实现多语言支持。
- 支持语言: 英语 (en)、简体中文 (zh)、西班牙语 (es)。
- 实现方式:
  - 使用 `arb` 文件存储翻译字符串（`lib/l10n/app_en.arb`, `app_zh.arb`, `app_es.arb`）。
  - 通过 `Localizations` 加载语言资源，动态切换。

## 3. 核心功能实现

### 3.1 情绪记录与反馈
- **页面**: `MoodRecordScreen`
- **功能**:
  - 显示情绪标签按钮（“焦虑”“平静”“低落”）。
  - 滑动条调整情绪强度（1-10）。
  - 文本输入框（50字以内）记录情绪原因。
  - 保存数据到 `SharedPreferences`。
- **反馈**: 根据情绪强度显示鼓励语（从预设JSON加载）。
- **文件**:
  - `lib/screens/mood_record_screen.dart`
  - `lib/models/mood.dart` (数据模型)
  - `lib/services/mood_service.dart` (数据存储与反馈逻辑)

### 3.2 快速疗愈工具箱
- **页面**: `TherapyToolsScreen`
- **工具**:
  1. **深呼吸**:
     - 使用 `AnimationController` 实现圆形放大/缩小动画（吸气4秒，呼气4秒）。
     - 文件: `lib/widgets/breathing_animation.dart`
  2. **正念片刻**:
     - 使用 `just_audio` 播放1分钟预录MP3音频。
     - 文件: `lib/widgets/mindfulness_player.dart`
     - 音频资源: `assets/audio/mindfulness.mp3`
  3. **积极语句**:
     - 从 `assets/data/affirmations.json` 随机加载语句。
     - 用户可收藏语句，保存到 `SharedPreferences`。
     - 文件: `lib/widgets/affirmation_card.dart`
- **文件**: `lib/screens/therapy_tools_screen.dart`

### 3.3 情绪趋势图
- **页面**: `MoodTrendScreen`
- **功能**:
  - 使用 `fl_chart` 绘制7天情绪强度折线图。
  - 数据从 `SharedPreferences` 读取，显示简单总结。
- **文件**:
  - `lib/screens/mood_trend_screen.dart`
  - `lib/widgets/mood_chart.dart`

### 3.4 每日提醒
- **功能**:
  - 使用 `flutter_local_notifications` 实现每日本地通知。
  - 用户可通过设置页面 (`SettingsScreen`) 调整提醒时间。
- **文件**:
  - `lib/services/notification_service.dart`
  - `lib/screens/settings_screen.dart`

## 4. 项目结构
```
moodmend/
├── android/
├── ios/
├── lib/
│   ├── l10n/
│   │   ├── app_en.arb
│   │   ├── app_zh.arb
│   │   ├── app_es.arb
│   ├── models/
│   │   ├── mood.dart
│   ├── screens/
│   │   ├── mood_record_screen.dart
│   │   ├── therapy_tools_screen.dart
│   │   ├── mood_trend_screen.dart
│   │   ├── settings_screen.dart
│   ├── services/
│   │   ├── mood_service.dart
│   │   ├── notification_service.dart
│   ├── widgets/
│   │   ├── breathing_animation.dart
│   │   ├── mindfulness_player.dart
│   │   ├── affirmation_card.dart
│   │   ├── mood_chart.dart
│   ├── main.dart
├── assets/
│   ├── audio/
│   │   ├── mindfulness.mp3
│   ├── data/
│   │   ├── affirmations.json
├── pubspec.yaml
```

## 5. 核心代码示例

### 5.1 `pubspec.yaml`
```yaml
name: moodmend
description: A simple mood healing app
version: 1.0.0
publish_to: 'none'

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  shared_preferences: ^2.2.0
  fl_chart: ^0.68.0
  just_audio: ^0.9.0
  flutter_local_notifications: ^17.0.0
  intl: ^0.19.0

flutter:
  uses-material-design: true
  assets:
    - assets/audio/mindfulness.mp3
    - assets/data/affirmations.json
  generate: true
```

### 5.2 `lib/main.dart`
```dart
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:moodmend/l10n/l10n.dart';
import 'package:moodmend/screens/mood_record_screen.dart';

void main() {
  runApp(const MoodMendApp());
}

class MoodMendApp extends StatelessWidget {
  const MoodMendApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MoodMend',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        scaffoldBackgroundColor: Colors.blue[50],
      ),
      supportedLocales: L10n.all,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      home: const MoodRecordScreen(),
    );
  }
}
```

### 5.3 国际化配置 (`lib/l10n/app_en.arb`)
```json
{
  "@@locale": "en",
  "appTitle": "MoodMend",
  "recordMood": "Record Your Mood",
  "anxious": "Anxious",
  "calm": "Calm",
  "low": "Low",
  "breathingTool": "Deep Breathing",
  "mindfulnessTool": "Mindfulness Moment",
  "affirmationTool": "Positive Affirmation"
}
```

## 6. Cursor IDE规则

### 6.1 `.cursor/rules.json`
```json
{
  "rules": [
    {
      "id": "moodmend-naming",
      "description": "Ensure consistent naming for MoodMend components",
      "pattern": ".*(Screen|Widget|Service)$",
      "message": "Class names should end with 'Screen', 'Widget', or 'Service' for clarity."
    },
    {
      "id": "moodmend-l10n",
      "description": "Ensure all UI strings use AppLocalizations",
      "pattern": "Text\\(['\"](.*?)['\"]\\)",
      "message": "Use AppLocalizations for text strings to support internationalization."
    },
    {
      "id": "moodmend-asset-path",
      "description": "Ensure assets are referenced from assets/ folder",
      "pattern": "assets/[^\\s]*",
      "message": "Asset paths must start with 'assets/' and be declared in pubspec.yaml."
    }
  ]
}
```

### 6.2 Cursor使用建议
- **自动补全**: 启用Dart/Flutter补全，建议导入 `material.dart` 和项目内模块。
- **代码检查**: 配置 `dart.analyze` 检测未使用的国际化字符串和资产引用。
- **重构支持**: 使用Cursor的重命名功能，确保 `Mood` 模型和 `Service` 类命名一致。
- **调试**: 在VS Code中配置 `launch.json` 以支持Android/iOS模拟器调试。

## 7. 实现注意事项
- **国际化**:
  - 使用 `flutter gen-l10n` 自动生成国际化代码。
  - 确保所有UI字符串通过 `AppLocalizations` 访问。
- **性能**:
  - 音频文件压缩到<1MB，减少加载时间。
  - 使用 `const` 构造函数优化Widget性能。
- **双端支持**:
  - 测试Android (minSdk 21) 和 iOS (minVersion 12.0)。
  - 确保通知权限请求适配iOS。
- **可行性**:
  - 开发周期：1-2个月（1-2人）。
  - 维护成本低，资源本地化，无需后端。

## 8. 后续迭代建议
- 添加社区分享功能（需后端支持）。
- 引入AI推荐个性化疗愈内容（需xAI API）。
- 扩展更多语言支持。