import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AppLocalization {
  // Supported locales
  static const List<Locale> supportedLocales = [
    Locale('en', ''), // English
    Locale('zh', ''), // Chinese (Simplified)
    Locale('es', ''), // Spanish
  ];

  // Localization delegates
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = [
    AppLocalizations.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ];

  // Language options for settings
  static const Map<String, String> languageOptions = {
    'en': 'English',
    'zh': '中文',
    'es': 'Español',
  };

  // Get language name by locale code
  static String getLanguageName(String localeCode) {
    return languageOptions[localeCode] ?? 'English';
  }

  // Get locale from language code
  static Locale getLocaleFromCode(String code) {
    switch (code) {
      case 'zh':
        return const Locale('zh', '');
      case 'es':
        return const Locale('es', '');
      default:
        return const Locale('en', '');
    }
  }

  // Check if locale is supported
  static bool isLocaleSupported(Locale locale) {
    return supportedLocales.any((supportedLocale) =>
        supportedLocale.languageCode == locale.languageCode);
  }

  // Get default locale
  static Locale get defaultLocale => const Locale('en', '');

  // Locale resolution callback
  static Locale? localeResolutionCallback(
    Locale? locale,
    Iterable<Locale> supportedLocales,
  ) {
    if (locale == null) {
      return defaultLocale;
    }

    final locales = [locale];

    // Try to find exact match
    for (final locale in locales) {
      if (isLocaleSupported(locale)) {
        return locale;
      }
    }

    // Try to find language match
    for (final locale in locales) {
      for (final supportedLocale in supportedLocales) {
        if (supportedLocale.languageCode == locale.languageCode) {
          return supportedLocale;
        }
      }
    }

    // Return default locale
    return defaultLocale;
  }
}
