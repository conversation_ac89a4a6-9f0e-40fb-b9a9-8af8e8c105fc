// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'MoodMend';

  @override
  String get recordMood => 'Record Your Mood';

  @override
  String get anxious => 'Anxious';

  @override
  String get calm => 'Calm';

  @override
  String get low => 'Low';

  @override
  String get breathingTool => 'Deep Breathing';

  @override
  String get mindfulnessTool => 'Mindfulness Moment';

  @override
  String get affirmationTool => 'Positive Affirmation';

  @override
  String get moodIntensity => 'Mood Intensity';

  @override
  String get moodReason => 'What\'s on your mind? (optional)';

  @override
  String get save => 'Save';

  @override
  String get therapyTools => 'Therapy Tools';

  @override
  String get moodTrend => 'Mood Trend';

  @override
  String get settings => 'Settings';

  @override
  String get last7Days => 'Last 7 Days';

  @override
  String get dailyReminder => 'Daily Reminder';

  @override
  String get reminderTime => 'Reminder Time';

  @override
  String get language => 'Language';

  @override
  String get inhale => 'Inhale';

  @override
  String get exhale => 'Exhale';

  @override
  String get start => 'Start';

  @override
  String get stop => 'Stop';

  @override
  String get play => 'Play';

  @override
  String get pause => 'Pause';

  @override
  String get favorite => 'Favorite';

  @override
  String get unfavorite => 'Unfavorite';

  @override
  String get moodSaved => 'Mood saved successfully!';

  @override
  String get encouragementLow => 'It\'s okay to feel this way. Tomorrow is a new day.';

  @override
  String get encouragementMedium => 'You\'re doing great! Keep taking care of yourself.';

  @override
  String get encouragementHigh => 'Wonderful! Your positive energy is shining through.';
}
