// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get appTitle => 'MoodMend';

  @override
  String get recordMood => 'Registra tu Estado de Ánimo';

  @override
  String get anxious => 'Ansioso';

  @override
  String get calm => 'Tranquilo';

  @override
  String get low => 'Decaído';

  @override
  String get breathingTool => 'Respiración Profunda';

  @override
  String get mindfulnessTool => 'Momento de Atención Plena';

  @override
  String get affirmationTool => 'Afirmación Positiva';

  @override
  String get moodIntensity => 'Intensidad del Estado de Ánimo';

  @override
  String get moodReason => '¿Qué tienes en mente? (opcional)';

  @override
  String get save => 'Guardar';

  @override
  String get therapyTools => 'Herramientas de Terapia';

  @override
  String get moodTrend => 'Tendencia del Estado de Ánimo';

  @override
  String get settings => 'Configuración';

  @override
  String get last7Days => 'Últimos 7 Días';

  @override
  String get dailyReminder => 'Recordatorio Diario';

  @override
  String get reminderTime => 'Hora del Recordatorio';

  @override
  String get language => 'Idioma';

  @override
  String get inhale => 'Inhalar';

  @override
  String get exhale => 'Exhalar';

  @override
  String get start => 'Iniciar';

  @override
  String get stop => 'Detener';

  @override
  String get play => 'Reproducir';

  @override
  String get pause => 'Pausar';

  @override
  String get favorite => 'Favorito';

  @override
  String get unfavorite => 'Quitar de Favoritos';

  @override
  String get moodSaved => '¡Estado de ánimo guardado exitosamente!';

  @override
  String get encouragementLow => 'Está bien sentirse así. Mañana es un nuevo día.';

  @override
  String get encouragementMedium => '¡Lo estás haciendo genial! Sigue cuidándote.';

  @override
  String get encouragementHigh => '¡Maravilloso! Tu energía positiva está brillando.';
}
