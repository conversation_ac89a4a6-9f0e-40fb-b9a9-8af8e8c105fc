import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../services/mood_service.dart';

class AffirmationCard extends StatefulWidget {
  const AffirmationCard({super.key});

  @override
  State<AffirmationCard> createState() => _AffirmationCardState();
}

class _AffirmationCardState extends State<AffirmationCard> {
  final MoodService _moodService = MoodService();
  String _currentAffirmation = '';
  bool _isFavorite = false;
  bool _isLoading = true;
  List<String> _affirmations = [];

  @override
  void initState() {
    super.initState();
    _loadAffirmations();
  }

  Future<void> _loadAffirmations() async {
    try {
      final String jsonString = await rootBundle.loadString('assets/data/affirmations.json');
      final Map<String, dynamic> data = json.decode(jsonString);
      
      // Get current locale
      final locale = Localizations.localeOf(context).languageCode;
      _affirmations = List<String>.from(data[locale] ?? data['en']);
      
      if (_affirmations.isNotEmpty) {
        await _getRandomAffirmation();
      }
    } catch (e) {
      debugPrint('Error loading affirmations: $e');
      // Fallback affirmations
      _affirmations = [
        'I am worthy of love and happiness.',
        'I choose peace over worry.',
        'I am stronger than my challenges.',
      ];
      await _getRandomAffirmation();
    }
    
    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _getRandomAffirmation() async {
    if (_affirmations.isEmpty) return;
    
    final random = Random();
    final newAffirmation = _affirmations[random.nextInt(_affirmations.length)];
    
    final isFavorite = await _moodService.isAffirmationFavorite(newAffirmation);
    
    setState(() {
      _currentAffirmation = newAffirmation;
      _isFavorite = isFavorite;
    });
  }

  Future<void> _toggleFavorite() async {
    if (_currentAffirmation.isEmpty) return;
    
    if (_isFavorite) {
      await _moodService.removeFavoriteAffirmation(_currentAffirmation);
    } else {
      await _moodService.saveFavoriteAffirmation(_currentAffirmation);
    }
    
    setState(() {
      _isFavorite = !_isFavorite;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          _isFavorite 
              ? AppLocalizations.of(context)?.favorite ?? 'Added to favorites'
              : AppLocalizations.of(context)?.unfavorite ?? 'Removed from favorites',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          margin: const EdgeInsets.all(20),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.orange.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.orange.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Icon(
                Icons.format_quote,
                size: 40,
                color: Colors.orange,
              ),
              const SizedBox(height: 16),
              Text(
                _currentAffirmation,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.orange.shade700,
                      fontWeight: FontWeight.w500,
                      height: 1.4,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton.icon(
                    onPressed: _getRandomAffirmation,
                    icon: const Icon(Icons.refresh),
                    label: const Text('New'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: _toggleFavorite,
                    icon: Icon(
                      _isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: _isFavorite ? Colors.red : Colors.grey,
                      size: 28,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
