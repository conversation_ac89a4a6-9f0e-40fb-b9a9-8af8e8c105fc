import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../widgets/breathing_animation.dart';
import '../widgets/mindfulness_player.dart';
import '../widgets/affirmation_card.dart';

class TherapyToolsScreen extends StatefulWidget {
  const TherapyToolsScreen({super.key});

  @override
  State<TherapyToolsScreen> createState() => _TherapyToolsScreenState();
}

class _TherapyToolsScreenState extends State<TherapyToolsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.therapyTools),
        backgroundColor: Colors.blue[50],
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: const Icon(Icons.air),
              text: l10n.breathingTool,
            ),
            Tab(
              icon: const Icon(Icons.self_improvement),
              text: l10n.mindfulnessTool,
            ),
            Tab(
              icon: const Icon(Icons.format_quote),
              text: l10n.affirmationTool,
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildBreathingTab(),
          _buildMindfulnessTab(),
          _buildAffirmationTab(),
        ],
      ),
    );
  }

  Widget _buildBreathingTab() {
    final l10n = AppLocalizations.of(context)!;
    
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Text(
            l10n.breathingTool,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Take a moment to focus on your breathing. Follow the circle as it expands and contracts.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const Expanded(
            child: BreathingAnimation(),
          ),
        ],
      ),
    );
  }

  Widget _buildMindfulnessTab() {
    final l10n = AppLocalizations.of(context)!;
    
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Text(
            l10n.mindfulnessTool,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Listen to this calming audio to help center yourself and find peace in the present moment.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const Expanded(
            child: MindfulnessPlayer(),
          ),
        ],
      ),
    );
  }

  Widget _buildAffirmationTab() {
    final l10n = AppLocalizations.of(context)!;
    
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Text(
            l10n.affirmationTool,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.orange,
                ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Read these positive affirmations to boost your mood and self-confidence.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const Expanded(
            child: AffirmationCard(),
          ),
        ],
      ),
    );
  }
}
