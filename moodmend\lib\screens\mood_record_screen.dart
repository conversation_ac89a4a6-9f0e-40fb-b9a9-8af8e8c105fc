import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/mood.dart';
import '../services/mood_service.dart';
import 'therapy_tools_screen.dart';
import 'mood_trend_screen.dart';
import 'settings_screen.dart';

class MoodRecordScreen extends StatefulWidget {
  const MoodRecordScreen({super.key});

  @override
  State<MoodRecordScreen> createState() => _MoodRecordScreenState();
}

class _MoodRecordScreenState extends State<MoodRecordScreen> {
  final MoodService _moodService = MoodService();
  final TextEditingController _reasonController = TextEditingController();
  
  String _selectedMoodType = '';
  double _moodIntensity = 5.0;
  bool _isSaving = false;
  int _currentIndex = 0;

  final List<Map<String, dynamic>> _moodTypes = [
    {'key': 'anxious', 'icon': Icons.sentiment_very_dissatisfied, 'color': Colors.red},
    {'key': 'calm', 'icon': Icons.sentiment_satisfied, 'color': Colors.green},
    {'key': 'low', 'icon': Icons.sentiment_dissatisfied, 'color': Colors.orange},
  ];

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  Future<void> _saveMood() async {
    if (_selectedMoodType.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)?.recordMood ?? 'Please select a mood type'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final mood = Mood(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: _selectedMoodType,
        intensity: _moodIntensity.round(),
        reason: _reasonController.text.trim().isEmpty ? null : _reasonController.text.trim(),
        timestamp: DateTime.now(),
      );

      await _moodService.saveMood(mood);
      
      final encouragement = _moodService.getEncouragementMessage(_moodIntensity.round());
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(AppLocalizations.of(context)?.moodSaved ?? 'Mood saved successfully!'),
                const SizedBox(height: 4),
                Text(
                  encouragement,
                  style: const TextStyle(fontStyle: FontStyle.italic),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );
        
        // Reset form
        setState(() {
          _selectedMoodType = '';
          _moodIntensity = 5.0;
          _reasonController.clear();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving mood: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Widget _buildMoodSelector() {
    final l10n = AppLocalizations.of(context)!;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.recordMood,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: _moodTypes.map((moodType) {
            final isSelected = _selectedMoodType == moodType['key'];
            String label;
            switch (moodType['key']) {
              case 'anxious':
                label = l10n.anxious;
                break;
              case 'calm':
                label = l10n.calm;
                break;
              case 'low':
                label = l10n.low;
                break;
              default:
                label = moodType['key'];
            }
            
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedMoodType = moodType['key'];
                });
              },
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? moodType['color'].withOpacity(0.2)
                      : Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected 
                        ? moodType['color']
                        : Colors.grey.withOpacity(0.3),
                    width: 2,
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      moodType['icon'],
                      size: 40,
                      color: isSelected 
                          ? moodType['color']
                          : Colors.grey,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      label,
                      style: TextStyle(
                        color: isSelected 
                            ? moodType['color']
                            : Colors.grey,
                        fontWeight: isSelected 
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildIntensitySlider() {
    final l10n = AppLocalizations.of(context)!;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${l10n.moodIntensity}: ${_moodIntensity.round()}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        Slider(
          value: _moodIntensity,
          min: 1,
          max: 10,
          divisions: 9,
          label: _moodIntensity.round().toString(),
          onChanged: (value) {
            setState(() {
              _moodIntensity = value;
            });
          },
        ),
        const Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('1', style: TextStyle(color: Colors.grey)),
            Text('10', style: TextStyle(color: Colors.grey)),
          ],
        ),
      ],
    );
  }

  Widget _buildReasonInput() {
    final l10n = AppLocalizations.of(context)!;
    
    return TextField(
      controller: _reasonController,
      maxLength: 50,
      maxLines: 2,
      decoration: InputDecoration(
        labelText: l10n.moodReason,
        border: const OutlineInputBorder(),
        hintText: 'Work stress, good news, etc.',
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    final List<Widget> pages = [
      _buildMoodRecordPage(),
      const TherapyToolsScreen(),
      const MoodTrendScreen(),
      const SettingsScreen(),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.appTitle),
        backgroundColor: Colors.blue[50],
        elevation: 0,
      ),
      body: pages[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.mood),
            label: l10n.recordMood,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.healing),
            label: l10n.therapyTools,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.trending_up),
            label: l10n.moodTrend,
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.settings),
            label: l10n.settings,
          ),
        ],
      ),
    );
  }

  Widget _buildMoodRecordPage() {
    final l10n = AppLocalizations.of(context)!;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildMoodSelector(),
          const SizedBox(height: 32),
          _buildIntensitySlider(),
          const SizedBox(height: 24),
          _buildReasonInput(),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: _isSaving ? null : _saveMood,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isSaving
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    l10n.save,
                    style: const TextStyle(fontSize: 18),
                  ),
          ),
        ],
      ),
    );
  }
}
