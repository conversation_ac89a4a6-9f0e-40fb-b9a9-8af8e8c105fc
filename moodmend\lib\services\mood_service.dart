import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/mood.dart';

class MoodService {
  static const String _moodsKey = 'moods';
  static const String _favoritesKey = 'favorite_affirmations';

  // Save a mood entry
  Future<void> saveMood(Mood mood) async {
    final prefs = await SharedPreferences.getInstance();
    final moodsJson = prefs.getString(_moodsKey) ?? '[]';
    final List<dynamic> moodsList = json.decode(moodsJson);
    
    moodsList.add(mood.toJson());
    await prefs.setString(_moodsKey, json.encode(moodsList));
  }

  // Get all mood entries
  Future<List<Mood>> getMoods() async {
    final prefs = await SharedPreferences.getInstance();
    final moodsJson = prefs.getString(_moodsKey) ?? '[]';
    final List<dynamic> moodsList = json.decode(moodsJson);
    
    return moodsList.map((json) => Mood.fromJson(json)).toList();
  }

  // Get moods for the last 7 days
  Future<List<Mood>> getRecentMoods() async {
    final allMoods = await getMoods();
    final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
    
    return allMoods
        .where((mood) => mood.timestamp.isAfter(sevenDaysAgo))
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  // Get encouragement message based on mood intensity
  String getEncouragementMessage(int intensity) {
    if (intensity <= 3) {
      return "It's okay to feel this way. Tomorrow is a new day.";
    } else if (intensity <= 7) {
      return "You're doing great! Keep taking care of yourself.";
    } else {
      return "Wonderful! Your positive energy is shining through.";
    }
  }

  // Save favorite affirmation
  Future<void> saveFavoriteAffirmation(String affirmation) async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesJson = prefs.getString(_favoritesKey) ?? '[]';
    final List<dynamic> favoritesList = json.decode(favoritesJson);
    
    if (!favoritesList.contains(affirmation)) {
      favoritesList.add(affirmation);
      await prefs.setString(_favoritesKey, json.encode(favoritesList));
    }
  }

  // Remove favorite affirmation
  Future<void> removeFavoriteAffirmation(String affirmation) async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesJson = prefs.getString(_favoritesKey) ?? '[]';
    final List<dynamic> favoritesList = json.decode(favoritesJson);
    
    favoritesList.remove(affirmation);
    await prefs.setString(_favoritesKey, json.encode(favoritesList));
  }

  // Get favorite affirmations
  Future<List<String>> getFavoriteAffirmations() async {
    final prefs = await SharedPreferences.getInstance();
    final favoritesJson = prefs.getString(_favoritesKey) ?? '[]';
    final List<dynamic> favoritesList = json.decode(favoritesJson);
    
    return favoritesList.cast<String>();
  }

  // Check if affirmation is favorite
  Future<bool> isAffirmationFavorite(String affirmation) async {
    final favorites = await getFavoriteAffirmations();
    return favorites.contains(affirmation);
  }
}
