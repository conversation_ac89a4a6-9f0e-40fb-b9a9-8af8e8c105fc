class Mood {
  final String id;
  final String type; // 'anxious', 'calm', 'low'
  final int intensity; // 1-10
  final String? reason;
  final DateTime timestamp;

  Mood({
    required this.id,
    required this.type,
    required this.intensity,
    this.reason,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'intensity': intensity,
      'reason': reason,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory Mood.fromJson(Map<String, dynamic> json) {
    return Mood(
      id: json['id'],
      type: json['type'],
      intensity: json['intensity'],
      reason: json['reason'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  @override
  String toString() {
    return 'Mood(id: $id, type: $type, intensity: $intensity, reason: $reason, timestamp: $timestamp)';
  }
}
