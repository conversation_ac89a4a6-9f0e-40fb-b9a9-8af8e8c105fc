import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../models/mood.dart';
import '../services/mood_service.dart';
import '../config/app_config.dart';
import '../config/app_constants.dart';
import '../widgets/common/styled_components.dart';
import 'therapy_tools_screen.dart';
import 'mood_trend_screen.dart';
import 'settings_screen.dart';

class MoodRecordScreen extends StatefulWidget {
  const MoodRecordScreen({super.key});

  @override
  State<MoodRecordScreen> createState() => _MoodRecordScreenState();
}

class _MoodRecordScreenState extends State<MoodRecordScreen> {
  final MoodService _moodService = MoodService();
  final TextEditingController _reasonController = TextEditingController();

  String _selectedMoodType = '';
  double _moodIntensity = 5.0;
  bool _isSaving = false;
  int _currentIndex = 0;

  final AppConfig _appConfig = AppConfig();

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  Future<void> _saveMood() async {
    if (_selectedMoodType.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)?.recordMood ??
              'Please select a mood type'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final mood = Mood(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: _selectedMoodType,
        intensity: _moodIntensity.round(),
        reason: _reasonController.text.trim().isEmpty
            ? null
            : _reasonController.text.trim(),
        timestamp: DateTime.now(),
      );

      await _moodService.saveMood(mood);

      final encouragement =
          _moodService.getEncouragementMessage(_moodIntensity.round());

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(AppLocalizations.of(context)?.moodSaved ??
                    'Mood saved successfully!'),
                const SizedBox(height: 4),
                Text(
                  encouragement,
                  style: const TextStyle(fontStyle: FontStyle.italic),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );

        // Reset form
        setState(() {
          _selectedMoodType = '';
          _moodIntensity = 5.0;
          _reasonController.clear();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving mood: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Widget _buildMoodSelector() {
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.recordMood,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: _appConfig.moodTypes.map((moodConfig) {
            final isSelected = _selectedMoodType == moodConfig.type;
            String label;
            switch (moodConfig.type) {
              case AppConstants.moodAnxious:
                label = l10n.anxious;
                break;
              case AppConstants.moodCalm:
                label = l10n.calm;
                break;
              case AppConstants.moodLow:
                label = l10n.low;
                break;
              default:
                label = moodConfig.type;
            }

            return MoodCard(
              moodType: moodConfig.type,
              icon: moodConfig.icon,
              color: moodConfig.color,
              label: label,
              isSelected: isSelected,
              onTap: () {
                setState(() {
                  _selectedMoodType = moodConfig.type;
                });
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildIntensitySlider() {
    final l10n = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${l10n.moodIntensity}: ${_moodIntensity.round()}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        Slider(
          value: _moodIntensity,
          min: 1,
          max: 10,
          divisions: 9,
          label: _moodIntensity.round().toString(),
          onChanged: (value) {
            setState(() {
              _moodIntensity = value;
            });
          },
        ),
        const Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('1', style: TextStyle(color: Colors.grey)),
            Text('10', style: TextStyle(color: Colors.grey)),
          ],
        ),
      ],
    );
  }

  Widget _buildReasonInput() {
    final l10n = AppLocalizations.of(context)!;

    return StyledTextField(
      controller: _reasonController,
      labelText: l10n.moodReason,
      hintText: 'Work stress, good news, etc.',
      maxLength: AppConstants.maxReasonLength,
      maxLines: 2,
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    final List<Widget> pages = [
      _buildMoodRecordPage(),
      const TherapyToolsScreen(),
      const MoodTrendScreen(),
      const SettingsScreen(),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.appTitle),
        backgroundColor: Colors.blue[50],
        elevation: 0,
      ),
      body: pages[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: _appConfig.bottomNavItems.map((navConfig) {
          String label;
          switch (navConfig.id) {
            case 'mood_record':
              label = l10n.recordMood;
              break;
            case 'therapy_tools':
              label = l10n.therapyTools;
              break;
            case 'mood_trend':
              label = l10n.moodTrend;
              break;
            case 'settings':
              label = l10n.settings;
              break;
            default:
              label = navConfig.id;
          }

          return BottomNavigationBarItem(
            icon: Icon(navConfig.icon),
            label: label,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildMoodRecordPage() {
    final l10n = AppLocalizations.of(context)!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildMoodSelector(),
          const SizedBox(height: 32),
          _buildIntensitySlider(),
          const SizedBox(height: 24),
          _buildReasonInput(),
          const SizedBox(height: 32),
          StyledButton(
            text: l10n.save,
            onPressed: _saveMood,
            isLoading: _isSaving,
          ),
        ],
      ),
    );
  }
}
