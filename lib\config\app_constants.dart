class AppConstants {
  // App information
  static const String appName = 'MoodMend';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'A simple mood healing app';

  // Storage keys
  static const String moodsStorageKey = 'moods';
  static const String favoritesStorageKey = 'favorite_affirmations';
  static const String reminderTimeKey = 'reminder_time';
  static const String reminderEnabledKey = 'reminder_enabled';
  static const String languageKey = 'selected_language';
  static const String themeKey = 'selected_theme';

  // Mood types
  static const String moodAnxious = 'anxious';
  static const String moodCalm = 'calm';
  static const String moodLow = 'low';

  // Mood intensity ranges
  static const int minMoodIntensity = 1;
  static const int maxMoodIntensity = 10;
  static const int defaultMoodIntensity = 5;

  // Mood intensity thresholds for encouragement
  static const int lowMoodThreshold = 3;
  static const int mediumMoodThreshold = 7;

  // Text input limits
  static const int maxReasonLength = 50;
  static const int maxAffirmationLength = 200;

  // Chart settings
  static const int trendDays = 7;
  static const double chartHeight = 300.0;
  static const double chartPadding = 16.0;

  // Animation settings
  static const int breathingInhaleDuration = 4; // seconds
  static const int breathingExhaleDuration = 4; // seconds
  static const double breathingMinScale = 0.5;
  static const double breathingMaxScale = 1.0;

  // Audio settings
  static const String mindfulnessAudioPath = 'assets/audio/mindfulness.mp3';
  static const Duration mindfulnessAudioDuration = Duration(minutes: 1);

  // Notification settings
  static const int dailyReminderNotificationId = 0;
  static const String notificationChannelId = 'daily_reminder';
  static const String notificationChannelName = 'Daily Mood Reminder';
  static const String notificationChannelDescription = 'Daily reminder to record your mood';
  static const int defaultReminderHour = 9; // 9 AM
  static const int defaultReminderMinute = 0;

  // Asset paths
  static const String affirmationsDataPath = 'assets/data/affirmations.json';
  static const String audioAssetsPath = 'assets/audio/';
  static const String dataAssetsPath = 'assets/data/';

  // URLs (for future use)
  static const String privacyPolicyUrl = 'https://example.com/privacy';
  static const String supportUrl = 'https://example.com/support';
  static const String feedbackUrl = 'https://example.com/feedback';

  // Validation rules
  static const int minPasswordLength = 6;
  static const int maxUsernameLength = 20;

  // UI dimensions
  static const double bottomNavHeight = 60.0;
  static const double appBarHeight = 56.0;
  static const double fabSize = 56.0;
  static const double avatarSize = 40.0;
  static const double iconSize = 24.0;
  static const double largeIconSize = 48.0;

  // Grid settings
  static const int moodGridColumns = 3;
  static const double moodCardAspectRatio = 1.0;

  // Snackbar settings
  static const Duration snackbarDuration = Duration(seconds: 3);
  static const Duration longSnackbarDuration = Duration(seconds: 5);

  // Loading timeouts
  static const Duration networkTimeout = Duration(seconds: 30);
  static const Duration shortTimeout = Duration(seconds: 5);

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Error messages
  static const String genericErrorMessage = 'Something went wrong. Please try again.';
  static const String networkErrorMessage = 'Network error. Please check your connection.';
  static const String validationErrorMessage = 'Please check your input and try again.';

  // Success messages
  static const String moodSavedMessage = 'Mood saved successfully!';
  static const String settingsUpdatedMessage = 'Settings updated successfully!';
  static const String reminderSetMessage = 'Reminder set successfully!';

  // Feature flags (for future use)
  static const bool enableAnalytics = false;
  static const bool enableCrashReporting = false;
  static const bool enableBetaFeatures = false;

  // Development settings
  static const bool isDebugMode = true;
  static const bool enableLogging = true;
  static const bool enablePerformanceMonitoring = false;
}
